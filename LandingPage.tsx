import React, { useState, useEffect, useRef } from 'react';
import Button from './components/ui/Button';
import Loader from './components/ui/Loader';
import { UsersIcon } from './components/icons/UsersIcon';
import { BeakerIcon } from './components/icons/BeakerIcon';
import { RecycleIcon } from './components/icons/RecycleIcon';
import { TrendingUpIcon } from './components/icons/TrendingUpIcon';
import { PulseCraftLogo } from './components/icons/PulseCraftLogo';
import { TwitterIcon } from './components/icons/TwitterIcon';
import { LinkedInIcon } from './components/icons/LinkedInIcon';
import { InstagramIcon } from './components/icons/InstagramIcon';
import { supabase } from './services/supabaseClient';
import type { Provider } from '@supabase/supabase-js';


const AuthForm: React.FC = () => {
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [accessCode, setAccessCode] = useState('');
    const [isSignUp, setIsSignUp] = useState(true);
    const [feedback, setFeedback] = useState({ message: '', type: 'info' });
    const [loading, setLoading] = useState(false);

    const handleAuth = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!email) return;

        setLoading(true);
        setFeedback({ message: '', type: 'info' });

        if (isSignUp) {
            // Early access code is OPTIONAL. If provided, must match.
            const providedCode = accessCode.trim();
            const earlyAccessCode = import.meta.env.VITE_EARLY_ACCESS_CODE || 'PULSECRAFT2025';
            let isEarly = false;

            if (providedCode) {
                if (providedCode !== earlyAccessCode) {
                    setFeedback({ message: 'Invalid early access code.', type: 'error' });
                    setLoading(false);
                    return;
                }
                isEarly = true;
            }

            const { data, error } = await supabase.auth.signUp({ email, password });
            if (error) {
                setFeedback({ message: error.message, type: 'error' });
                setLoading(false);
                return;
            }

            // Initialize profile with starting credits and flags.
            const userId = data?.user?.id;
            if (userId) {
                const initialCredits = isEarly ? 500 : 200;
                await supabase
                    .from('profiles')
                    .upsert({
                        id: userId,
                        app_data: {
                            settings: { credits: initialCredits },
                            onboardingData: { hasOnboarded: false, pioneerPromoRedeemed: false },
                            earlyAccessUsed: isEarly
                        }
                    });
            }

            setFeedback({ message: 'Success! Please check your email for a confirmation link.', type: 'success' });
        } else { // Sign In
            const { data, error } = await supabase.auth.signInWithPassword({ email, password });
            if (error) setFeedback({ message: error.message, type: 'error' });
            // On success, the onAuthStateChange listener will handle the redirect.
            // After sign in, ensure profile exists. If missing, create with baseline credits (200).
            if (data?.user) {
                const { data: profile } = await supabase.from('profiles').select('id, app_data').eq('id', data.user.id).single();
                if (!profile) {
                    await supabase.from('profiles').insert({
                        id: data.user.id,
                        app_data: {
                            settings: { credits: 200 },
                            onboardingData: { hasOnboarded: false, pioneerPromoRedeemed: false },
                            earlyAccessUsed: false
                        }
                    });
                }
            }
        }
        setLoading(false);
    };
    
    const handleOAuthSignIn = async (provider: Provider) => {
        setLoading(true);
        const { error } = await supabase.auth.signInWithOAuth({ provider });
        if (error) {
            setFeedback({ message: error.message, type: 'error' });
            setLoading(false);
        }
    };

    return (
        <div className="w-full max-w-md mx-auto bg-slate-800/60 p-6 lg:p-8 rounded-xl border border-slate-700 backdrop-blur-lg">
            <h2 className="text-xl lg:text-2xl font-bold text-center text-white mb-2 heading-font">{isSignUp ? 'Get Early Access' : 'Welcome Back'}</h2>
            <p className="text-center text-slate-400 mb-6 text-sm">
                {isSignUp ? "Sign up to start building your brand's pulse." : "Sign in to continue your work."}
            </p>
            {feedback.message && (
                <div className={`p-3 rounded-md mb-4 text-sm text-center ${feedback.type === 'error' ? 'bg-red-500/20 text-red-300' : 'bg-green-500/20 text-green-300'}`}>
                    {feedback.message}
                </div>
            )}
            <form onSubmit={handleAuth} className="space-y-4">
                <div>
                    <label className="text-xs font-medium text-slate-300 block mb-1" htmlFor="email">Email</label>
                    <input
                        id="email"
                        type="email"
                        value={email}
                        onChange={e => setEmail(e.target.value)}
                        required
                        placeholder="<EMAIL>"
                        className="input-field min-h-[44px]"
                    />
                </div>
                <div>
                    <label className="text-xs font-medium text-slate-300 block mb-1" htmlFor="password">Password</label>
                    <input
                        id="password"
                        type="password"
                        value={password}
                        onChange={e => setPassword(e.target.value)}
                        required
                        minLength={6}
                        placeholder="••••••••"
                        className="input-field min-h-[44px]"
                    />
                </div>
                {isSignUp && (
                     <div>
                         <label className="text-xs font-medium text-slate-300 block mb-1" htmlFor="accessCode">Early Access Code</label>
                         <input
                            id="accessCode"
                            type="text"
                            value={accessCode}
                            onChange={e => setAccessCode(e.target.value)}
                            placeholder="Optional — enter your code"
                            className="input-field min-h-[44px]"
                        />
                    </div>
                )}
                <Button type="submit" variant="primary" size="md" className="w-full py-3 text-base min-h-[44px]" disabled={loading}>
                    {loading ? <Loader size="sm" /> : (isSignUp ? 'Create Account' : 'Sign In')}
                </Button>
            </form>
            {/* 
             <div className="relative my-6">
                <div className="absolute inset-0 flex items-center" aria-hidden="true"><div className="w-full border-t border-slate-700" /></div>
                <div className="relative flex justify-center text-sm"><span className="bg-slate-800/60 px-2 text-slate-400">Or continue with</span></div>
            </div>
            <Button onClick={() => handleOAuthSignIn('google')} variant="outline" size="md" className="w-full py-3 !text-white !border-slate-600 hover:!bg-slate-700">
                <svg className="w-5 h-5 mr-2" viewBox="0-0 48 48"><path fill="#FFC107" d="M43.611 20.083H42V20H24v8h11.303c-1.649 4.657-6.08 8-11.303 8c-6.627 0-12-5.373-12-12s5.373-12 12-12c3.059 0 5.842 1.154 7.961 3.039l5.657-5.657C34.046 6.053 29.268 4 24 4C12.955 4 4 12.955 4 24s8.955 20 20 20s20-8.955 20-20c0-1.341-.138-2.65-.389-3.917z"></path><path fill="#FF3D00" d="M6.306 14.691l6.571 4.819C14.655 15.108 18.961 12 24 12c3.059 0 5.842 1.154 7.961 3.039l5.657-5.657C34.046 6.053 29.268 4 24 4C16.318 4 9.656 8.337 6.306 14.691z"></path><path fill="#4CAF50" d="M24 44c5.166 0 9.86-1.977 13.409-5.192l-6.19-5.238A11.91 11.91 0 0 1 24 36c-6.627 0-12-5.373-12-12s5.373-12 12-12c5.822 0 10.771 4.012 11.754 9.417l5.138-5.138C41.156 10.423 33.266 4 24 4C12.955 4 4 12.955 4 24s8.955 20 20 20z"></path><path fill="#1976D2" d="M43.611 20.083H24v8h11.303c-.792 2.237-2.231 4.166-4.087 5.571l6.19 5.238C42.018 35.27 44 30.022 44 24c0-1.341-.138-2.65-.389-3.917z"></path></svg>
                Continue with Google
            </Button>
            */}
            <p className="text-center mt-6 text-sm text-slate-400">
                {isSignUp ? 'Already have an account?' : "Don't have an account?"}{' '}
                <button onClick={() => setIsSignUp(!isSignUp)} className="font-semibold text-purple-400 hover:text-purple-300">
                    {isSignUp ? 'Sign In' : 'Sign Up'}
                </button>
            </p>
            <style>{`.input-field { width: 100%; padding: 0.75rem 1rem; color: white; background-color: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 0.5rem; transition: border-color 0.2s, box-shadow 0.2s; } .input-field:focus { outline: none; border-color: #9333ea; box-shadow: 0 0 0 2px rgba(147, 51, 234, 0.3); }`}</style>
        </div>
    );
}

const LogoPlaceholder: React.FC<{ name: string }> = ({ name }) => (
    <div className="font-bold tracking-widest text-slate-500 text-lg">{name}</div>
);


const FeatureCard: React.FC<{ icon: React.ReactNode; title: string; children: React.ReactNode }> = ({ icon, title, children }) => (
    <div className="bg-slate-800/50 p-6 rounded-xl border border-slate-700 backdrop-blur-sm transition-all duration-300 hover:bg-slate-800 hover:-translate-y-1">
        <div className="flex items-center justify-center h-12 w-12 rounded-full bg-purple-500/10 text-purple-300 mb-4 border border-purple-500/30">
            {icon}
        </div>
        <h3 style={{fontFamily: "'Space Grotesk', sans-serif"}} className="text-xl font-bold text-white mb-2">{title}</h3>
        <p className="text-slate-400">{children}</p>
    </div>
);

const LandingPage: React.FC = () => {
    const [waitingListFeedback, setWaitingListFeedback] = useState({ message: '', type: '' });
    const [waitingListLoading, setWaitingListLoading] = useState(false);

    useEffect(() => {
        const handleMouseMove = (e: MouseEvent) => {
            document.documentElement.style.setProperty('--mouse-x', `${e.clientX}px`);
            document.documentElement.style.setProperty('--mouse-y', `${e.clientY}px`);
        };
        window.addEventListener('mousemove', handleMouseMove);
        return () => window.removeEventListener('mousemove', handleMouseMove);
    }, []);

    return (
        <div className="bg-slate-900 text-white min-h-screen overflow-x-hidden relative">
            <style>{`
                .heading-font { font-family: 'Space Grotesk', sans-serif; }
                .animated-gradient-bg {
                    width: 100%;
                    height: 100%;
                    position: fixed;
                    top: 0;
                    left: 0;
                    background: radial-gradient(circle at 20% 20%, #9333ea 0%, transparent 30%), radial-gradient(circle at 80% 70%, #d946ef 0%, transparent 30%);
                    opacity: 0.2;
                    pointer-events: none;
                    z-index: 0;
                }
                /* Disable animation on mobile for better performance and scrolling */
                @media (min-width: 768px) {
                    .animated-gradient-bg {
                        animation: morph 20s ease-in-out infinite;
                        will-change: transform;
                        opacity: 0.3;
                    }
                }
                @keyframes morph {
                    0%, 100% { transform: translate(0, 0) rotate(0deg); }
                    25% { transform: translate(5%, -5%) rotate(45deg); }
                    50% { transform: translate(-5%, 5%) rotate(90deg); }
                    75% { transform: translate(2.5%, 2.5%) rotate(135deg); }
                }
                .section-container {
                    padding-top: 3rem;
                    padding-bottom: 3rem;
                    position: relative;
                    z-index: 1;
                }
                @media (min-width: 768px) {
                    .section-container {
                        padding-top: 6rem;
                        padding-bottom: 6rem;
                    }
                }
            `}</style>
            <div className="animated-gradient-bg"></div>
            
            <div className="relative z-10 flex flex-col min-h-screen w-full">
                <header className="sticky top-0 z-20 bg-slate-900/50 backdrop-blur-lg border-b border-slate-800">
                    <div className="container mx-auto px-4 sm:px-6 py-3 flex justify-between items-center max-w-7xl">
                        <PulseCraftLogo isWordmark={true} />
                    </div>
                </header>

                <main className="container mx-auto px-4 sm:px-6 flex-grow max-w-7xl w-full">
                    <section className="grid lg:grid-cols-2 gap-8 lg:gap-16 items-center pt-16 pb-12 lg:pt-32 lg:pb-24">
                        <div className="max-w-xl text-center lg:text-left order-2 lg:order-1">
                            <span className="inline-block px-4 py-2 text-xs font-semibold tracking-wider text-teal-300 uppercase bg-teal-500/10 rounded-full">
                                Now in Early Access
                            </span>
                            <h1 className="mt-6 text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-extrabold tracking-tight heading-font bg-clip-text text-transparent bg-gradient-to-r from-white to-slate-400 leading-tight">
                                Stop Guessing. Start Strategizing.
                            </h1>
                            <p className="mt-6 text-base sm:text-lg lg:text-xl text-slate-300 leading-relaxed">
                                PulseCraft is your AI Brand Strategist. It moves beyond random posts by building a deep, cohesive content plan and then automates your entire workflow.
                            </p>
                        </div>
                        <div className="w-full order-1 lg:order-2">
                            <AuthForm />
                        </div>
                    </section>

                    <section className="text-center py-16">
                        <div className="max-w-xl mx-auto">
                            <h2 className="text-3xl sm:text-4xl font-bold tracking-tight text-white heading-font">Join the Waiting List</h2>
                            <p className="mt-4 text-lg text-slate-400">
                                Be the first to know when PulseCraft is available to the public.
                            </p>
                            <form className="mt-8 flex flex-col sm:flex-row gap-4" onSubmit={async (e: React.FormEvent<HTMLFormElement>) => {
                                e.preventDefault();
                                const emailInput = e.currentTarget.elements.namedItem('email') as HTMLInputElement;
                                const email = emailInput.value;
                                if (!email) return;

                                setWaitingListLoading(true);
                                setWaitingListFeedback({ message: '', type: '' });

                                try {
                                    const functionName = 'join-waiting-list';
                                    const edgeFunctionUrl = `${import.meta.env.VITE_SUPABASE_URL}/functions/v1/${functionName}`;

                                    const response = await fetch(edgeFunctionUrl, {
                                        method: 'POST',
                                        headers: { 'Content-Type': 'application/json' },
                                        body: JSON.stringify({ email }),
                                    });

                                    const result = await response.json();
                                    if (response.ok) {
                                        setWaitingListFeedback({ message: result.message || 'Thank you for joining!', type: 'success' });
                                        emailInput.value = '';
                                    } else {
                                        setWaitingListFeedback({ message: result.error || 'An error occurred.', type: 'error' });
                                    }
                                } catch (error) {
                                    setWaitingListFeedback({ message: 'An unexpected error occurred. Please try again.', type: 'error' });
                                } finally {
                                    setWaitingListLoading(false);
                                }
                            }}>
                                <input type="email" name="email" placeholder="<EMAIL>" required className="input-field flex-grow" />
                                <Button type="submit" variant="secondary" size="md" className="py-3 text-base" disabled={waitingListLoading}>
                                    {waitingListLoading ? <Loader size="sm" /> : 'Join Now'}
                                </Button>
                            </form>
                            {waitingListFeedback.message && (
                                <div className={`p-3 rounded-md mt-4 text-sm text-center transition-all duration-300 ${waitingListFeedback.type === 'error' ? 'bg-red-500/20 text-red-300' : 'bg-green-500/20 text-green-300'}`}>
                                    {waitingListFeedback.message}
                                </div>
                            )}
                        </div>
                    </section>
                    
                    <section className="text-center py-16">
                        <p className="text-sm font-semibold text-slate-400 uppercase tracking-widest">TRUSTED BY THE NEXT WAVE OF CREATIVE BRANDS</p>
                        <div className="mt-8 flex justify-center items-center gap-x-10 sm:gap-x-16 opacity-70 flex-wrap">
                            <LogoPlaceholder name="AURA" /> <LogoPlaceholder name="RHYTHM" />
                            <LogoPlaceholder name="ETHOS" /> <LogoPlaceholder name="STORY" />
                            <LogoPlaceholder name="CULT" />
                        </div>
                    </section>
                    
                    <section className="section-container">
                        <div className="text-center max-w-3xl mx-auto">
                            <h2 className="text-3xl sm:text-4xl font-bold tracking-tight text-white heading-font">From Spark to Strategy in 3 Steps</h2>
                        </div>
                        <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 md:gap-4 max-w-6xl mx-auto relative">
                            <div className="absolute top-1/2 left-0 w-full h-px bg-gradient-to-r from-transparent via-purple-500/50 to-transparent hidden md:block"></div>
                             <div className="relative text-center">
                                <div className="flex items-center justify-center w-16 h-16 mx-auto rounded-full bg-slate-800 border-2 border-purple-500 mb-4 text-purple-400 font-bold text-xl heading-font">1</div>
                                <h3 className="heading-font text-xl font-bold text-white mb-2">Define Your DNA</h3>
                                <p className="text-slate-400">Input your brand's context, voice, and visual style. Tell us who you are and who you want to reach.</p>
                            </div>
                            <div className="relative text-center">
                                 <div className="flex items-center justify-center w-16 h-16 mx-auto rounded-full bg-slate-800 border-2 border-purple-500 mb-4 text-purple-400 font-bold text-xl heading-font">2</div>
                                <h3 className="heading-font text-xl font-bold text-white mb-2">Ignite the AI Engine</h3>
                                <p className="text-slate-400">Select a strategic framework and let PulseCraft generate a comprehensive, long-term content plan.</p>
                            </div>
                            <div className="relative text-center">
                                 <div className="flex items-center justify-center w-16 h-16 mx-auto rounded-full bg-slate-800 border-2 border-purple-500 mb-4 text-purple-400 font-bold text-xl heading-font">3</div>
                                <h3 className="heading-font text-xl font-bold text-white mb-2">Launch Your Narrative</h3>
                                <p className="text-slate-400">Generate weeks of ready-to-post content, complete with captions, hashtags, and image prompts.</p>
                            </div>
                        </div>
                    </section>

                    <section className="section-container">
                        <div className="text-center max-w-3xl mx-auto">
                            <h2 className="text-3xl sm:text-4xl font-bold tracking-tight text-white heading-font">A Smarter Toolkit for Modern Brands</h2>
                            <p className="mt-4 text-lg text-slate-400">PulseCraft provides benefits that go beyond simple content creation, helping you build a more resonant and effective brand presence.</p>
                        </div>
                        <div className="mt-16 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                            <FeatureCard icon={<UsersIcon className="w-6 h-6" />} title="Build Your Tribe">
                               Stop shouting into the void. Create content that speaks directly to your ideal audience by choosing a framework like **'Seth Godin Tribe Building'** in your AI Config.
                            </FeatureCard>
                            <FeatureCard icon={<BeakerIcon className="w-6 h-6" />} title="Strategic A/B Testing">
                               Don't guess what works. Generate multiple strategic plans with different frameworks to test which voice connects most with your audience.
                            </FeatureCard>
                            <FeatureCard icon={<RecycleIcon className="w-6 h-6" />} title="Infinite Content Remixing">
                                Turn one great idea into a dozen posts. Use the **'Adapt Post'** feature in the post editor to instantly spin a single concept into unique, platform-native content.
                            </FeatureCard>
                            <FeatureCard icon={<TrendingUpIcon className="w-6 h-6" />} title="Stay Ahead of the Curve">
                               Is your plan feeling stale? Use the **'Refine Plan'** Co-pilot in the Strategy Brief to inject real-time trends and ideas into your existing strategy without starting over.
                            </FeatureCard>
                        </div>
                    </section>
                </main>

            <footer className="relative z-10 border-t border-slate-800">
                <div className="container mx-auto px-4 sm:px-6 py-8 lg:py-12 max-w-7xl">
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                        <div className="col-span-1 md:col-span-1">
                            <PulseCraftLogo isWordmark={true} />
                            <p className="mt-4 text-slate-400 text-sm">Your AI Brand Strategist for cohesive, automated content creation.</p>
                        </div>
                        <div className="col-span-1">
                            <h3 className="font-semibold text-white tracking-wider uppercase">Product</h3>
                            <ul className="mt-4 space-y-2 text-sm">
                                <li><a href="#" className="text-slate-400 hover:text-white">Features</a></li>
                                <li><a href="#" className="text-slate-400 hover:text-white">Pricing</a></li>
                                <li><a href="#" className="text-slate-400 hover:text-white">Changelog</a></li>
                            </ul>
                        </div>
                        <div className="col-span-1">
                            <h3 className="font-semibold text-white tracking-wider uppercase">Company</h3>
                            <ul className="mt-4 space-y-2 text-sm">
                                <li><a href="#" className="text-slate-400 hover:text-white">About</a></li>
                                <li><a href="#" className="text-slate-400 hover:text-white">Blog</a></li>
                                <li><a href="#" className="text-slate-400 hover:text-white">Contact</a></li>
                            </ul>
                        </div>
                        <div className="col-span-1">
                            <h3 className="font-semibold text-white tracking-wider uppercase">Legal</h3>
                            <ul className="mt-4 space-y-2 text-sm">
                                <li><a href="#" className="text-slate-400 hover:text-white">Privacy Policy</a></li>
                                <li><a href="#" className="text-slate-400 hover:text-white">Terms of Service</a></li>
                            </ul>
                        </div>
                    </div>
                    <div className="mt-12 pt-8 border-t border-slate-800 flex flex-col sm:flex-row justify-between items-center">
                        <p className="text-sm text-slate-500">&copy; {new Date().getFullYear()} PulseCraft. All rights reserved.</p>
                        <div className="flex space-x-4 mt-4 sm:mt-0">
                            <a href="#" className="text-slate-500 hover:text-white"><TwitterIcon className="h-5 w-5" /></a>
                            <a href="#" className="text-slate-500 hover:text-white"><LinkedInIcon className="h-5 w-5" /></a>
                            <a href="#" className="text-slate-500 hover:text-white"><InstagramIcon className="h-5 w-5" /></a>
                        </div>
                    </div>
                </div>
            </footer>
            </div>
        </div>
    );
};

export default LandingPage;
