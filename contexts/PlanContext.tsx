import React, { createContext, useContext, useState, ReactNode } from 'react';
import { WeekData, PlanDuration, Post } from '../types';
import { useSettings } from './AppContext';
import { supabase } from '../services/supabaseClient';
import { generateContentPlanStream, refineContentPlan, generatePostVariations } from '../services/geminiService';
import { COSTS } from './AppContext';

interface PlanContextType {
    planMarkdown: string;
    completedWeeks: string[];
    handleGeneratePlan: () => Promise<boolean>;
    handleRefinePlan: (refinementInput: string) => Promise<{ newPlan: string; diff: string } | null>;
    handleGenerateWeeklyBatch: (weekId: string, weekData: WeekData, flatWeekIds: string[], instructions?: string) => Promise<void>;
    handleGenerateFirstMonth: () => Promise<boolean>;
    handlePlanUpdate: (newPlan: string) => Promise<void>;
    handlePlanDurationChange: (duration: PlanDuration) => Promise<void>;
}

export const PlanContext = createContext<PlanContextType | undefined>(undefined);

export const PlanProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
    const { settings, checkCredits, handleSaveSettings, modifyCredits } = useSettings();
    const [planMarkdown, setPlanMarkdown] = useState<string>('');
    const [completedWeeks, setCompletedWeeks] = useState<string[]>([]);
    const [posts, setPosts] = useState<Post[]>([]);
    const [loadingStates, setLoadingStates] = useState({
        plan: false, refine: false, weeklyBatch: null as string | null, firstMonth: false,
    });

    const handlePlanUpdate = async (newPlan: string) => {
        setPlanMarkdown(newPlan);
    };

    const handlePlanDurationChange = async (duration: PlanDuration) => {
        await handleSaveSettings({ planDuration: duration });
    };

    const handleGeneratePlan = async (): Promise<boolean> => {
        if (!checkCredits(COSTS.PLAN_GENERATION)) return false;
        setPlanMarkdown('');
        setLoadingStates(s => ({...s, plan: true}));
        modifyCredits(-COSTS.PLAN_GENERATION, `Generated ${settings.planDuration} strategy plan`);
        try {
            const stream = generateContentPlanStream(settings);
            let fullPlan = '';
            for await (const chunk of stream) {
                fullPlan += chunk;
                setPlanMarkdown(fullPlan);
            }
            return true;
        } catch (e) {
            modifyCredits(COSTS.PLAN_GENERATION, 'Failed plan generation - Refund');
            return false;
        } finally {
            setLoadingStates(s => ({...s, plan: false}));
        }
    };

    const handleRefinePlan = async (refinementInput: string): Promise<{ newPlan: string; diff: string } | null> => {
        if (!checkCredits(COSTS.PLAN_REFINEMENT)) return null;
        setLoadingStates(s => ({...s, refine: true}));
        modifyCredits(-COSTS.PLAN_REFINEMENT, `Refined plan`);
        try {
            const result = await refineContentPlan(settings, planMarkdown, refinementInput);
            if (!result || !result.newPlan) throw new Error("AI did not return a valid refinement.");
            return result;
        } catch (e) {
            modifyCredits(COSTS.PLAN_REFINEMENT, `Failed plan refinement - Refund`);
            return null;
        } finally {
            setLoadingStates(s => ({...s, refine: false}));
        }
    };

    const handleGenerateWeeklyBatch = async (weekId: string, weekData: WeekData, flatWeekIds: string[], instructions?: string): Promise<void> => {
        if (!checkCredits(COSTS.WEEKLY_POST_BATCH)) return;
        setLoadingStates(s => ({ ...s, weeklyBatch: weekId }));
        modifyCredits(-COSTS.WEEKLY_POST_BATCH, `Generated posts for ${weekId.split(' | ').pop()}`);

        try {
            let allNewPosts: Omit<Post, 'id'>[] = [];
            for (const postIdea of weekData.postIdeas) {
                const variations = await generatePostVariations(settings, weekData, postIdea, instructions);
                if (!variations) continue;

                const lastScheduledDate = posts.reduce((latest, post) => (post.scheduledDate && new Date(post.scheduledDate) > latest ? new Date(post.scheduledDate) : latest), new Date(0));
                const scheduleStartDate = new Date(Math.max(new Date().getTime(), lastScheduledDate.getTime()));
                scheduleStartDate.setDate(scheduleStartDate.getDate() + 1);

                const newPostsForIdea = variations.map((variation, i) => {
                    const scheduleDate = new Date(scheduleStartDate);
                    scheduleDate.setDate(scheduleDate.getDate() + i * 2); // Stagger posts
                    scheduleDate.setHours(9 + Math.floor(Math.random() * 9));
                    return {
                        platform: variation.platform,
                        caption: variation.caption,
                        hashtags: variation.hashtags,
                        image_prompt: variation.image_prompt,
                        likes: Math.floor(Math.random() * 1000),
                        comments: Math.floor(Math.random() * 100),
                        date: new Date().toISOString(),
                        scheduledDate: scheduleDate.toISOString(),
                        weekId: weekId,
                        status: 'scheduled' as const,
                    };
                });
                allNewPosts.push(...newPostsForIdea);
            }

            if (allNewPosts.length === 0) {
                throw new Error("AI did not return any post variations for any idea.");
            }

            setPosts(prev => {
                const nextId = prev.length > 0 ? Math.max(...prev.map(p => p.id)) + 1 : 1;
                return [...prev, ...allNewPosts.map((p, i) => ({ ...p, id: nextId + i }))];
            });
            setCompletedWeeks(prev => [...new Set([...prev, weekId])]);

        } catch (e) {
            modifyCredits(COSTS.WEEKLY_POST_BATCH, `Failed batch generation for ${weekId} - Refund`);
        } finally {
            setLoadingStates(s => ({ ...s, weeklyBatch: null }));
        }
    };

    const handleGenerateFirstMonth = async (): Promise<boolean> => {
        return true;
    };

    return (
        <PlanContext.Provider value={{
            planMarkdown,
            completedWeeks,
            handleGeneratePlan,
            handleRefinePlan,
            handleGenerateWeeklyBatch,
            handleGenerateFirstMonth,
            handlePlanUpdate,
            handlePlanDurationChange,
        }}>
            {children}
        </PlanContext.Provider>
    );
};

export const usePlan = () => {
    const context = useContext(PlanContext);
    if (context === undefined) {
        throw new Error('usePlan must be used within a PlanProvider');
    }
    return context;
};