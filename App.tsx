

import React, { useEffect } from 'react';
import { View } from './types';
import Sidebar from './components/Sidebar';
import ProfileHeader from './components/ProfileHeader';
import Dashboard from './components/Dashboard';
import ContentPlannerView from './components/ContentPlannerView';
import StrategyView from './components/StrategyView';
import PostModal from './components/PostModal';
import SettingsModal from './components/SettingsModal';
import EarlyAdopterModal from './components/EarlyAdopterModal';
import FeedbackModal from './components/FeedbackModal';
import ToastContainer from './components/ToastContainer';
import PlanSummaryPanel from './components/PlanSummaryPanel';
import ImageSelectionModal from './components/ImageSelectionModal';
import Button from './components/ui/Button';
import Loader from './components/ui/Loader';
import { BookOpenIcon } from './components/icons/BookOpenIcon';
import OnboardingGuide from './components/OnboardingGuide';
import WelcomeModal from './components/WelcomeModal';
import AmbassadorModal from './components/AmbassadorModal';
import AskIhabModal from './components/AskIhabModal';
import { IhabBotIcon } from './components/icons/IhabBotIcon';
import { useUI, usePlan, useApp } from './contexts/AppContext';
import { ModalProvider, useModals } from './contexts/ModalContext';

const pageTitles: Record<View, string> = {
    dashboard: 'Dashboard',
    planner: 'Content Planner',
    strategy: 'Strategy Brief'
};

const ProfileSetupBanner: React.FC<{ onGoToSettings: () => void }> = ({ onGoToSettings }) => (
    <div className="bg-amber-100 border-b-2 border-amber-300 text-amber-900 p-3 text-sm text-center flex items-center justify-center shrink-0">
        <p className="font-medium"><strong>Welcome!</strong> To get the best results, complete your brand profile in the settings.</p>
        <Button variant="outline" size="xs" onClick={onGoToSettings} className="ml-4 border-amber-400 text-amber-900 hover:bg-amber-200">Go to Settings</Button>
    </div>
);


const AppContent: React.FC = () => {
    const {
        view,
        toasts,
        selectedPost,
        imageSelectionOptions,
        isSubmittingFeedback,
        onboardingState,
        isAppLoading,
        isProfileSetupNeeded,
        setView,
        setToasts,
        setImageSelectionOptions,
        setOnboardingState,
        setInitialSettingsTab,
    } = useUI();

    const {
        isSettingsOpen,
        isEarlyAdopterModalOpen,
        isFeedbackModalOpen,
        isPlanSummaryOpen,
        isAmbassadorModalOpen,
        isAskIhabModalOpen,
        isWelcomeModalOpen,
        setWelcomeModalOpen,
        setAskIhabModalOpen,
        setSettingsOpen,
    } = useModals();

    const { planMarkdown } = usePlan();
    const { ONBOARDING_STEPS, handleStartTour } = useApp();

    useEffect(() => {
        // Open the welcome modal for new users
        if (isEarlyAdopterModalOpen) {
            setWelcomeModalOpen(true);
        }
    }, [isEarlyAdopterModalOpen, setWelcomeModalOpen]);

    useEffect(() => {
        const handleMouseMove = (e: MouseEvent) => {
            document.documentElement.style.setProperty('--mouse-x', `${e.clientX}px`);
            document.documentElement.style.setProperty('--mouse-y', `${e.clientY}px`);
        };
        window.addEventListener('mousemove', handleMouseMove);
        return () => window.removeEventListener('mousemove', handleMouseMove);
    }, []);

    const renderView = () => {
        switch (view) {
            case 'dashboard':
                return <Dashboard />;
            case 'planner':
                return <ContentPlannerView />;
            case 'strategy':
                return <StrategyView />;
            default:
                return <Dashboard />;
        }
    };

    // if (isAppLoading) {
    //     return (
    //         <div className="flex h-screen w-screen items-center justify-center bg-slate-100">
    //             <Loader size="lg" />
    //         </div>
    //     );
    // }

    return (
        <div className="flex h-screen bg-slate-100">
            <ToastContainer toasts={toasts} onDismiss={id => setToasts(current => current.filter(t => t.id !== id))} />
            <OnboardingGuide 
                key={onboardingState.currentStep}
                steps={ONBOARDING_STEPS}
                onboardingState={onboardingState}
                setOnboardingState={setOnboardingState}
            />
            <Sidebar />
            <div className="flex-1 flex flex-col overflow-hidden">
                <ProfileHeader />
                {isProfileSetupNeeded && <ProfileSetupBanner onGoToSettings={() => { setSettingsOpen(true); setInitialSettingsTab('Profile'); }} />}
                <main className="flex-1 overflow-y-auto p-6">
                    {renderView()}
                </main>
            </div>

            {selectedPost && <PostModal />}
            <ImageSelectionModal
                isOpen={imageSelectionOptions.isOpen}
                isGenerating={imageSelectionOptions.isGenerating}
                images={imageSelectionOptions.images}
                onSelect={imageSelectionOptions.onSelect}
                onClose={() => setImageSelectionOptions(prev => ({ ...prev, isOpen: false }))}
            />
            {isSettingsOpen && <SettingsModal />}
            {isEarlyAdopterModalOpen && <EarlyAdopterModal />}
            {isFeedbackModalOpen && <FeedbackModal isSubmitting={isSubmittingFeedback} />}
            {isPlanSummaryOpen && <PlanSummaryPanel />}
            {isAmbassadorModalOpen && <AmbassadorModal />}
            <WelcomeModal 
                isOpen={isWelcomeModalOpen}
                onClose={() => setWelcomeModalOpen(false)}
                onStartOnboarding={() => {
                    setWelcomeModalOpen(false);
                    handleStartTour();
                }}
            />
            {isAskIhabModalOpen && <AskIhabModal />}

            <div className="fixed bottom-6 right-6 z-30">
                <button 
                    onClick={() => setAskIhabModalOpen(true)}
                    className="p-3 bg-purple-600 text-white rounded-full shadow-lg hover:bg-purple-700 transition-transform hover:scale-110 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-purple-500"
                    aria-label="Ask Ihab for help"
                >
                    <IhabBotIcon className="w-8 h-8" />
                </button>
            </div>
        </div>
    );
};


import { AuthProvider } from './contexts/AuthContext';

const App: React.FC = () => {
    return (
        <AuthProvider>
            <ModalProvider>
                <AppContent />
            </ModalProvider>
        </AuthProvider>
    );
};

export default App;
