import React, { useState, useRef } from 'react';
import { Settings, Platform, ALL_PLATFORMS, CreditTransaction, SettingsTab, CreditPack } from '../types';
import Modal from './ui/Modal';
import Button from './ui/Button';
import { strategyFrameworks, frameworkDescriptions } from '../strategyFrameworks';
import { AlertTriangleIcon } from './icons/AlertTriangleIcon';
import { useSettings, useUI, useApp, useIntegrations } from '../contexts/AppContext';
import StripeCheckoutForm from './StripeCheckoutForm';
import { loadStripe } from '@stripe/stripe-js';
import { Elements } from '@stripe/react-stripe-js';

const CREDIT_PACKS: CreditPack[] = [
    { name: 'Starter Pack', credits: 200, price: 9.99, description: 'Perfect for getting started and trying out features.' },
    { name: 'Creator Pack', credits: 500, price: 19.99, description: 'Ideal for professionals and small businesses.', bestValue: true },
    { name: 'Agency Pack', credits: 1250, price: 44.99, description: 'The best option for power users and agencies.' },
];

const stripePublishableKey = import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY || '';

if (!stripePublishableKey || stripePublishableKey.startsWith('pk_test_dummy_')) {
    console.warn(
        'Stripe Publishable Key (STRIPE_PUBLISHABLE_KEY) is not set in your environment. ' +
        'Using a dummy key. Real payments will fail. Please add a real key to your env config.'
    );
}
const stripePromise = loadStripe(stripePublishableKey || 'pk_test_dummy_key_for_testing_purposes_only');

const TabButton: React.FC<{ label: string; isActive: boolean; onClick: () => void; isDanger?: boolean; }> = ({ label, isActive, onClick, isDanger = false }) => (
    <button
        onClick={onClick}
        className={`px-3 py-2 text-sm font-semibold rounded-md transition-colors ${
            isActive
                ? (isDanger ? 'bg-red-100 text-red-700' : 'bg-purple-100 text-purple-700')
                : (isDanger ? 'text-red-600 hover:bg-red-50' : 'text-slate-600 hover:bg-slate-100')
        }`}
        aria-current={isActive ? 'page' : undefined}
    >
        {label}
    </button>
);

const CreditHistoryItem: React.FC<{ transaction: CreditTransaction }> = ({ transaction }) => (
    <li className="flex justify-between items-center py-2 border-b border-slate-100 last:border-b-0">
        <div>
            <p className="font-medium text-sm text-slate-700">{transaction.description}</p>
            <p className="text-xs text-slate-500">{new Date(transaction.timestamp).toLocaleString()}</p>
        </div>
        <span className={`font-bold text-sm ${transaction.amount > 0 ? 'text-green-600' : 'text-slate-800'}`}>
            {transaction.amount > 0 ? '+' : ''}{transaction.amount.toLocaleString()}
        </span>
    </li>
);

const SettingsModal: React.FC = () => {
    const { settings, handleSaveSettings, handleAdminAccess, handlePurchaseCredits, handleApplyPromoCode } = useSettings();
    const { integrations, handleConnectPlatform, handleDisconnectPlatform } = useIntegrations();
    const { setSettingsOpen } = useUI();
    const { handleExportData, handleImportData, handleResetApp } = useApp();
    
    const [localSettings, setLocalSettings] = useState<Omit<Settings, 'userId' | 'credits' | 'role' | 'creditHistory'>>(settings);
    const [adminCode, setAdminCode] = useState('');
    const [promoCode, setPromoCode] = useState('');
    const [activeTab, setActiveTab] = useState<SettingsTab | 'Integrations'>('Profile');
    const [packToPurchase, setPackToPurchase] = useState<CreditPack | null>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);

    const onClose = () => setSettingsOpen(false);

    const handleSave = () => {
        if(localSettings.platforms.length === 0) {
            alert("Please select at least one target platform."); return;
        }
        if (adminCode) {
            handleAdminAccess(adminCode);
            setAdminCode('');
        }
        handleSaveSettings(localSettings);
    };
    
    const handleConfirmPurchase = (pack: CreditPack) => {
        handlePurchaseCredits(pack);
        setPackToPurchase(null);
    };

    const handleFileImport = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (!file) return;
        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const data = JSON.parse(e.target?.result as string);
                handleImportData(data);
            } catch (error) {
                alert(`Import failed: ${error instanceof Error ? error.message : "Unknown error"}`);
            } finally {
                if(fileInputRef.current) fileInputRef.current.value = "";
            }
        };
        reader.readAsText(file);
    };
    
    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
        setLocalSettings(s => ({...s, [e.target.id]: e.target.value}));
    };

    const handlePlatformChange = (platform: Platform) => {
        setLocalSettings(s => ({ ...s, platforms: s.platforms.includes(platform) ? s.platforms.filter(p => p !== platform) : [...s.platforms, platform] }));
    };
    
    const handleRedeemPromo = () => {
        if (!promoCode.trim()) return;
        handleApplyPromoCode(promoCode.trim());
        setPromoCode('');
    };

    const renderContent = () => {
        switch(activeTab) {
            case 'Profile': return (
                <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div><label htmlFor="brandName" className="label">Brand Name:</label><input type="text" id="brandName" value={localSettings.brandName} onChange={handleChange} className="input" placeholder="e.g., Aura Collective"/></div>
                        <div><label htmlFor="generationLanguage" className="label">Generation Language:</label><select id="generationLanguage" value={localSettings.generationLanguage} onChange={handleChange} className="input"><option value="fr">Français</option><option value="en">English</option><option value="es">Español</option></select></div>
                    </div>
                     <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                         <div><label htmlFor="profileImageUrl" className="label">Profile Image URL:</label><input type="text" id="profileImageUrl" value={localSettings.profileImageUrl} onChange={handleChange} className="input" placeholder="https://example.com/image.png" /></div>
                        <div><label htmlFor="websiteUrl" className="label">Website URL:</label><input type="text" id="websiteUrl" value={localSettings.websiteUrl} onChange={handleChange} className="input" placeholder="https://example.com" /></div>
                     </div>
                     <div><label htmlFor="bio" className="label">Profile Bio:</label><textarea id="bio" value={localSettings.bio} onChange={handleChange} className="input h-24" placeholder="Describe your brand in a few words."/></div>
                     <div className="flex items-center space-x-2">
                        <input type="checkbox" id="showComingSoon" checked={localSettings.showComingSoon} onChange={e => setLocalSettings(s => ({...s, showComingSoon: e.target.checked}))} className="h-4 w-4 rounded border-slate-300 text-purple-600 focus:ring-purple-500" />
                        <label htmlFor="showComingSoon" className="text-sm font-medium text-slate-700">Show "Coming Soon" features</label>
                    </div>
                    {/* LinkedIn defaults and status */}
                    {integrations.some(i => i.platform === 'linkedin') && (
                        <div className="mt-6 p-4 bg-white border border-slate-200 rounded-lg">
                            <h4 className="text-md font-semibold text-slate-800">LinkedIn Defaults</h4>
                            <p className="text-xs text-slate-500 mt-1">
                                Choose your default posting identity for LinkedIn. You can still override per post in the editor.
                            </p>
                            <div className="mt-3 max-w-xs">
                                <label className="label">Default Posting As</label>
                                <select
                                    value={localSettings.defaultLinkedInAuthorType || 'person'}
                                    onChange={(e) =>
                                        setLocalSettings(s => ({ ...s, defaultLinkedInAuthorType: e.target.value as any }))
                                    }
                                    className="input"
                                >
                                    <option value="person">Person</option>
                                    {integrations.find(i => i.platform === 'linkedin' && (i as any).companyUrn) && (
                                        <option value="organization">Company</option>
                                    )}
                                </select>
                                <div className="mt-2 text-xs text-slate-500">
                                    {integrations.find(i => i.platform === 'linkedin' && (i as any).authorUrn) && (
                                        <div>Connected Member URN: <code className="bg-slate-100 px-1 rounded">{(integrations.find(i => i.platform === 'linkedin') as any)?.authorUrn}</code></div>
                                    )}
                                    {integrations.find(i => i.platform === 'linkedin' && (i as any).companyUrn) ? (
                                        <div>Connected Company URN: <code className="bg-slate-100 px-1 rounded">{(integrations.find(i => i.platform === 'linkedin') as any)?.companyUrn}</code></div>
                                    ) : (
                                        <div className="text-orange-600">Company page not connected for posting. Ensure the app has the w_organization_social scope and the user has page permissions.</div>
                                    )}
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            );
            case 'Integrations': return (
                <div className="space-y-6">
                    <div>
                        <h3 className="text-lg font-semibold text-slate-800">Target Platforms</h3>
                        <p className="text-sm text-slate-500">Select the social media platforms you want the AI to generate content for.</p>
                        <div className="mt-4 grid grid-cols-2 gap-4">
                            {ALL_PLATFORMS.map(platform => (
                                <label key={platform} className="flex items-center space-x-3 p-3 bg-white border border-slate-200 rounded-lg cursor-pointer hover:bg-slate-50">
                                    <input
                                        type="checkbox"
                                        className="h-4 w-4 rounded border-slate-300 text-purple-600 focus:ring-purple-500"
                                        checked={localSettings.platforms.includes(platform)}
                                        onChange={() => handlePlatformChange(platform)}
                                    />
                                    <span className="font-medium text-slate-700 capitalize">{platform === 'x-twitter' ? 'X (Twitter)' : platform}</span>
                                </label>
                            ))}
                        </div>
                    </div>
                    <div className="border-t border-slate-200"></div>
                    <div>
                        <h3 className="text-lg font-semibold text-slate-800">Connect Your Social Accounts</h3>
                        <p className="text-sm text-slate-500">Connect your accounts to allow PulseCraft to post on your behalf. This is optional and only needed for direct publishing.</p>
                        <div className="space-y-3 pt-4">
                            {ALL_PLATFORMS.map(platform => {
                                const isConnected = integrations.some(i => i.platform === platform);
                                const isLinkedIn = platform === 'linkedin';
                                return (
                                    <div key={platform} className="p-3 bg-white border border-slate-200 rounded-lg">
                                        <div className="flex items-center justify-between">
                                            <span className="capitalize font-medium text-slate-700">{platform === 'x-twitter' ? 'X (Twitter)' : platform}</span>
                                            {isConnected ? (
                                                <Button variant="danger_outline" onClick={() => handleDisconnectPlatform(platform)}>Disconnect</Button>
                                            ) : (
                                                <Button
                                                    variant="outline"
                                                    onClick={() => handleConnectPlatform(platform)}
                                                >
                                                    {platform === 'linkedin' ? 'Connect LinkedIn'
                                                        : platform === 'x-twitter' ? 'Connect X'
                                                        : platform === 'instagram' ? 'Connect Instagram'
                                                        : 'Connect Facebook'}
                                                </Button>
                                            )}
                                        </div>
                                        {isLinkedIn && integrations.some(i => i.platform === 'linkedin') && (
                                            <div className="mt-2 pt-2 border-t border-slate-100 text-xs text-slate-600">
                                                LinkedIn connected. You can pick your default posting identity below.
                                            </div>
                                        )}
                                    </div>
                                )
                            })}
                        </div>
                    </div>
                </div>
            );
            case 'AI Config': return (
                 <div className="space-y-4">
                     <div><label htmlFor="businessContext" className="label">Business Context (for AI):</label><textarea id="businessContext" value={localSettings.businessContext} onChange={handleChange} className="input h-32" placeholder="Describe your business, target audience, brand tone, etc."/></div>
                     <div>
                        <label htmlFor="strategyFramework" className="label">Strategy Framework:</label><select id="strategyFramework" value={localSettings.strategyFramework} onChange={handleChange} className="input">{strategyFrameworks.map(f => (<option key={f} value={f}>{f}</option>))}</select>
                        {frameworkDescriptions[localSettings.strategyFramework] && (<div className="mt-2 p-3 bg-purple-50 border border-purple-200 rounded-lg text-xs text-slate-600"><p className="font-bold text-purple-800">{frameworkDescriptions[localSettings.strategyFramework].title}</p><p className="mt-1"><strong className="font-semibold">Approach:</strong> {frameworkDescriptions[localSettings.strategyFramework].approach}</p><p className="mt-1"><strong className="font-semibold">Best For:</strong> {frameworkDescriptions[localSettings.strategyFramework].bestFor}</p></div>)}
                    </div>
                    <div><label htmlFor="characterDescription" className="label">Character / Tone Consistency:</label><textarea id="characterDescription" value={localSettings.characterDescription} onChange={handleChange} className="input h-24" placeholder="e.g., Embody 'Co-pilot', a friendly robot..."/><p className="help-text">The AI will adopt this persona.</p></div>
                     <div><label htmlFor="styleDescription" className="label">Visual Style Consistency:</label><textarea id="styleDescription" value={localSettings.styleDescription} onChange={handleChange} className="input h-24" placeholder="e.g., Minimalist 3D render, pastel colors..."/><p className="help-text">The AI will use this for image prompts.</p></div>
                </div>
            );
            case 'Account': return (
                 <div className="space-y-4">
                     <div><label className="label">Email</label><input type="text" value={settings.userId} readOnly className="input bg-slate-100 cursor-not-allowed" /><p className="help-text">Your email used for the tester program.</p></div>
                    <div><label className="label">Current Role</label><input type="text" value={settings.role} readOnly className="input bg-slate-100 cursor-not-allowed capitalize" /></div>
                    {settings.role !== 'admin' && (<div><label htmlFor="adminCode" className="label">Enter Admin Code</label><input type="password" id="adminCode" value={adminCode} onChange={e => setAdminCode(e.target.value)} className="input" placeholder="Enter code for unlimited access" /></div>)}
                    <div className="pt-2"><label className="label">Promo Code</label><div className="flex space-x-2"><input type="text" value={promoCode} onChange={e => setPromoCode(e.target.value)} className="input" placeholder="e.g., PULSECRAFTPIONEER" /><Button variant="outline" onClick={handleRedeemPromo} disabled={!promoCode.trim()}>Redeem</Button></div></div>
                    <div className="pt-2">
                        <label className="label">Credit History</label>
                        <div className="bg-white border border-slate-200 rounded-lg max-h-60 overflow-y-auto">
                            {settings.creditHistory.length > 0 ? <ul className="p-2">{settings.creditHistory.map(t => <CreditHistoryItem key={t.timestamp} transaction={t}/>)}</ul> : <p className="text-center text-sm text-slate-500 p-4">No transactions yet.</p>}
                        </div>
                    </div>
                 </div>
            );
            case 'Buy Credits': return (
                <div>
                    <h3 className="text-xl font-bold text-slate-800">Recharge Your Credits</h3><p className="mt-1 mb-6 text-slate-500">Choose a pack that fits your needs.</p>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        {CREDIT_PACKS.map(pack => (
                            <div key={pack.name} className={`p-6 border rounded-lg flex flex-col items-center text-center transition-all ${pack.bestValue ? 'bg-purple-50 border-purple-500 shadow-lg scale-105' : 'bg-white border-slate-200 hover:border-slate-300'}`}>
                                {pack.bestValue && <span className="text-xs font-bold text-white bg-purple-600 px-3 py-1 rounded-full -mt-9 mb-3">BEST VALUE</span>}
                                <h4 className="text-lg font-bold text-slate-800">{pack.name}</h4><p className="text-4xl font-extrabold text-purple-600 my-2">{pack.credits.toLocaleString()}</p><p className="text-slate-500 font-semibold">Credits</p><p className="text-sm text-slate-500 mt-2 flex-grow">{pack.description}</p>
                                <Button onClick={() => setPackToPurchase(pack)} className="mt-6 w-full" variant={pack.bestValue ? 'primary' : 'outline'}>Buy for ${pack.price}</Button>
                            </div>
                        ))}
                    </div>
                </div>
            );
            case 'Danger Zone': return (
                <div className="space-y-6">
                    <div className="p-4 bg-red-50 border-l-4 border-red-500 text-red-800 rounded-r-lg"><div className="flex"><div className="shrink-0"><AlertTriangleIcon className="h-5 w-5 text-red-400" aria-hidden="true" /></div><div className="ml-3"><h3 className="text-lg font-bold text-red-900">Danger Zone</h3><div className="mt-2 text-sm text-red-700"><p>These actions are destructive. Please proceed with caution.</p></div></div></div></div>
                    <div className="pt-4"><h3 className="font-bold text-lg text-slate-800">Data Management</h3><p className="help-text mb-2">Export your data for backup, or import a previously saved file.</p><div className="flex space-x-2"><Button onClick={handleExportData} variant="outline">Export Data</Button><Button onClick={() => fileInputRef.current?.click()} variant="outline">Import Data</Button><input type="file" ref={fileInputRef} onChange={handleFileImport} className="hidden" accept=".json"/></div></div>
                    <div className="pt-4"><h3 className="font-bold text-lg text-red-700">Reset Application</h3><p className="help-text mb-2 text-red-600/90">This will delete all generated content and progress. Account settings and credits will be kept. This is irreversible.</p><Button onClick={handleResetApp} variant="danger">Reset All Content</Button></div>
                </div>
            );
        }
    };

    return (
        <Modal isOpen={true} onClose={onClose} title="⚙️ Co-pilot Settings" size="3xl">
            <div className="flex-grow flex flex-col overflow-y-hidden -m-6 bg-slate-50">
                {/* Mobile Tab Navigation */}
                <div className="lg:hidden p-3 border-b border-slate-200 bg-white">
                    <select
                        value={activeTab}
                        onChange={(e) => setActiveTab(e.target.value as SettingsTab | 'Integrations')}
                        className="w-full input text-sm min-h-[44px]"
                    >
                        <option value="Profile">Profile</option>
                        <option value="Integrations">Integrations</option>
                        <option value="AI Config">AI Config</option>
                        <option value="Buy Credits">Buy Credits</option>
                        <option value="Account">Account</option>
                        <option value="Danger Zone">Danger Zone</option>
                    </select>
                </div>

                {/* Desktop Tab Navigation */}
                <div className="hidden lg:block p-4 border-b border-slate-200 bg-white">
                    <div className="flex items-center space-x-2 flex-wrap">
                        {['Profile', 'Integrations', 'AI Config', 'Buy Credits', 'Account', 'Danger Zone'].map(tab =>
                            <TabButton
                                key={tab}
                                label={tab}
                                isActive={activeTab === tab}
                                onClick={() => setActiveTab(tab as SettingsTab | 'Integrations')}
                                isDanger={tab === 'Danger Zone'}
                            />
                        )}
                    </div>
                </div>

                <div className="p-3 lg:p-6 flex-grow overflow-y-auto modal-content">{renderContent()}</div>

                {/* Mobile Footer */}
                <div className="lg:hidden p-3 border-t border-slate-200 bg-white">
                    <Button onClick={handleSave} variant="primary" className="w-full min-h-[44px]">
                        Save Settings
                    </Button>
                    <div className="text-xs text-slate-500 mt-2 text-center">
                        <strong>Note:</strong> API keys must be set in environment configuration.
                    </div>
                </div>

                {/* Desktop Footer */}
                <div className="hidden lg:flex p-4 border-t border-slate-200 items-center justify-between bg-white">
                    <div className="text-xs text-slate-500"><strong>Note:</strong> API keys and access codes must be set in your environment configuration.</div>
                    <Button onClick={handleSave} variant="primary">Save Settings</Button>
                </div>
            </div>
            {packToPurchase && (
                <Elements stripe={stripePromise}>
                    <StripeCheckoutForm 
                        pack={packToPurchase}
                        onClose={() => setPackToPurchase(null)}
                        onSuccess={handleConfirmPurchase}
                    />
                </Elements>
            )}
            <style>{`.label { display: block; color: #334155; font-size: 0.875rem; font-weight: 600; margin-bottom: 0.5rem; } .input { display: block; width: 100%; background-color: var(--card); box-shadow: inset 0 1px 2px 0 rgb(0 0 0 / 0.05); border-radius: 0.375rem; border: 1px solid var(--border); padding: 0.5rem 0.75rem; line-height: 1.5; color: var(--foreground); transition: all 0.2s ease-in-out; } .input:focus { outline: 2px solid transparent; outline-offset: 2px; border-color: var(--ring); box-shadow: 0 0 0 2px #e9d5ff; } .input::placeholder { color: #94a3b8; } .help-text { font-size: 0.75rem; color: var(--muted-foreground); margin-top: 0.25rem; }`}</style>
        </Modal>
    );
};

export default SettingsModal;
