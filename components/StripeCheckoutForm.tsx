import React, { useState } from 'react';
import { CreditPack } from '../types';
import Modal from './ui/Modal';
import Button from './ui/Button';
import Loader from './ui/Loader';
import { LockIcon } from './icons/LockIcon';
import { useStripe, useElements, CardElement } from '@stripe/react-stripe-js';
import { useApp } from '../contexts/AppContext';
import { supabase } from '../services/supabaseClient';

interface StripeCheckoutFormProps {
    pack: CreditPack;
    onClose: () => void;
    onSuccess: (pack: CreditPack) => void;
}

const cardElementOptions = {
    style: {
        base: {
            color: "#0f172a", // slate-900
            fontFamily: 'Inter, sans-serif',
            fontSmoothing: "antialiased",
            fontSize: "16px",
            "::placeholder": {
                color: "#94a3b8" // slate-400
            }
        },
        invalid: {
            color: "#ef4444", // red-500
            iconColor: "#ef4444"
        }
    },
    hidePostalCode: true,
};

const StripeCheckoutForm: React.FC<StripeCheckoutFormProps> = ({ pack, onClose, onSuccess }) => {
    const stripe = useStripe();
    const elements = useElements();
    const { user } = useApp();
    const [cardHolderName, setCardHolderName] = useState('');
    const [error, setError] = useState<string | null>(null);
    const [isProcessing, setIsProcessing] = useState(false);

    const handlePayment = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!stripe || !elements) {
            // Stripe.js has not yet loaded.
            setError("Payment system is not ready. Please try again in a moment.");
            return;
        }
        setIsProcessing(true);
        setError(null);
        
        const cardElement = elements.getElement(CardElement);
        if (!cardElement) {
            setError("Card details element not found.");
            setIsProcessing(false);
            return;
        }

        const { error: paymentMethodError, paymentMethod } = await stripe.createPaymentMethod({
            type: 'card',
            card: cardElement,
            billing_details: {
                name: cardHolderName,
            },
        });

        if (paymentMethodError) {
            setError(paymentMethodError.message || "An unknown error occurred.");
            setIsProcessing(false);
            return;
        }

        // Send payment to backend for processing
        try {
            if (!user) {
                setError("User not authenticated. Please log in and try again.");
                setIsProcessing(false);
                return;
            }

            // Map pack names to backend pack names
            const packNameMap: Record<string, string> = {
                'Starter Pack': 'starter',
                'Creator Pack': 'pro',
                'Agency Pack': 'unlimited'
            };

            const backendPackName = packNameMap[pack.name];
            if (!backendPackName) {
                setError("Invalid pack selected. Please try again.");
                setIsProcessing(false);
                return;
            }

            // Call the Supabase Edge Function
            const { data, error: functionError } = await supabase.functions.invoke('purchase-credits', {
                body: {
                    paymentMethodId: paymentMethod.id,
                    packName: backendPackName,
                    userId: user.id
                }
            });

            if (functionError) {
                console.error('Payment function error:', functionError);
                let errorMessage = functionError.message || "Payment processing failed. Please try again.";

                // Check for specific configuration errors
                if (errorMessage.includes('STRIPE_SECRET_KEY') || errorMessage.includes('stripe')) {
                    errorMessage = "Payment system is not fully configured. Please contact support or try again later.";
                }

                setError(errorMessage);
                setIsProcessing(false);
                return;
            }

            if (data?.error) {
                console.error('Payment error:', data.error);
                setError(data.error);
                setIsProcessing(false);
                return;
            }

            // Payment successful
            console.log('Payment successful:', data);
            setIsProcessing(false);
            onSuccess(pack);
        } catch (err) {
            console.error('Payment processing error:', err);
            setError(err instanceof Error ? err.message : "An unexpected error occurred. Please try again.");
            setIsProcessing(false);
        }
    };

    return (
        <Modal isOpen={true} onClose={onClose} title={`Secure Checkout`} size="lg">
            <div className="flex flex-col">
                <div className="p-6 bg-slate-50 rounded-lg border border-slate-200">
                    <div className="flex justify-between items-center">
                        <div>
                            <p className="text-lg font-bold text-slate-800">{pack.name}</p>
                            <p className="text-sm text-slate-500">{pack.credits.toLocaleString()} Credits</p>
                        </div>
                        <p className="text-2xl font-bold text-purple-600">${pack.price.toFixed(2)}</p>
                    </div>
                </div>

                <form onSubmit={handlePayment} className="mt-6 space-y-4">
                    <div>
                        <label htmlFor="card-holder" className="label">Card Holder Name</label>
                        <input
                            id="card-holder"
                            type="text"
                            className="input"
                            placeholder="Ihab B."
                            value={cardHolderName}
                            onChange={(e) => setCardHolderName(e.target.value)}
                            required
                            disabled={isProcessing}
                        />
                    </div>
                    <div>
                        <label className="label">Card Information</label>
                        <div className="p-3 border rounded-md bg-white border-slate-300 focus-within:border-purple-500 focus-within:ring-2 focus-within:ring-purple-200 transition-all">
                            <CardElement options={cardElementOptions} />
                        </div>
                    </div>
                    
                    {error && (
                        <div className="text-red-600 text-sm font-medium p-3 bg-red-50 rounded-md border border-red-200">
                            {error}
                        </div>
                    )}

                    <div className="pt-4">
                         <Button type="submit" className="w-full" disabled={isProcessing || !stripe}>
                            {isProcessing ? 
                                <><Loader size="sm" className="mr-2" /> Processing...</> : 
                                `Pay $${pack.price.toFixed(2)}`}
                        </Button>
                    </div>
                     <p className="text-xs text-center text-slate-400 flex items-center justify-center">
                        <LockIcon className="w-3 h-3 mr-1.5" /> Secure payment powered by Stripe.
                    </p>
                </form>
            </div>
            <style>{`.label { display: block; font-weight: 600; font-size: 0.875rem; color: #334155; margin-bottom: 0.5rem; } .input { display: block; width: 100%; border-radius: 0.375rem; border: 1px solid #e2e8f0; padding: 0.5rem 0.75rem; background-color: white; line-height: 1.5; color: #0f172a; } .input:focus { outline: 2px solid transparent; outline-offset: 2px; border-color: #9333ea; box-shadow: 0 0 0 2px #e9d5ff; }`}</style>
        </Modal>
    );
};

export default StripeCheckoutForm;