

import React, { useState, useMemo } from 'react';
import { Post, PlannerView } from '../types';
import PlatformIcon from './ui/PlatformIcon';
import Button from './ui/Button';
import { ChevronDownIcon } from './icons/ChevronDownIcon';

interface CalendarViewProps {
    posts: Post[];
    onPostClick: (post: Post) => void;
    onPostUpdate: (updatedPost: Post) => void;
    viewMode: 'month' | 'week' | 'day';
}

const hours = Array.from({ length: 24 }, (_, i) => i);

const CalendarView: React.FC<CalendarViewProps> = ({ posts, onPostClick, onPostUpdate, viewMode }) => {
    const [currentDate, setCurrentDate] = useState(new Date());
    const [draggedPostId, setDraggedPostId] = useState<number | null>(null);

    const handleDateChange = (amount: number) => {
        const newDate = new Date(currentDate);
        if (viewMode === 'month') newDate.setMonth(newDate.getMonth() + amount);
        else if (viewMode === 'week') newDate.setDate(newDate.getDate() + 7 * amount);
        else if (viewMode === 'day') newDate.setDate(newDate.getDate() + amount);
        setCurrentDate(newDate);
    };

    const handleToday = () => setCurrentDate(new Date());
    
    const handleDragStart = (e: React.DragEvent<HTMLDivElement>, post: Post) => {
        e.dataTransfer.setData("text/plain", post.id.toString());
        e.dataTransfer.effectAllowed = "move";
        setDraggedPostId(post.id);
    };

    const handleDrop = (e: React.DragEvent<HTMLDivElement>, targetDate: Date, targetHour?: number) => {
        e.preventDefault();
        const postId = parseInt(e.dataTransfer.getData("text/plain"), 10);
        const postToUpdate = posts.find(p => p.id === postId);

        if (postToUpdate) {
            const originalPostDate = new Date(postToUpdate.scheduledDate || postToUpdate.date);
            let newScheduledDate = new Date(targetDate);

            if (targetHour !== undefined) {
                const targetMinute = (e.nativeEvent.offsetY / e.currentTarget.offsetHeight) * 60;
                newScheduledDate.setHours(targetHour, targetMinute);
            } else {
                newScheduledDate.setHours(originalPostDate.getHours(), originalPostDate.getMinutes());
            }

            onPostUpdate({ ...postToUpdate, scheduledDate: newScheduledDate.toISOString() });
        }
        setDraggedPostId(null);
    };
    
    const renderMonthView = () => {
        const startOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
        const endOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);
        const startDate = new Date(startOfMonth);
        startDate.setDate(startDate.getDate() - startDate.getDay());
        const endDate = new Date(endOfMonth);
        endDate.setDate(endDate.getDate() + (6 - endDate.getDay()));

        const days = [];
        let day = new Date(startDate);
        while (day <= endDate) { days.push(new Date(day)); day.setDate(day.getDate() + 1); }

        const postsByDate = posts.reduce((acc, post) => {
            const dateKey = (post.scheduledDate || post.date).split('T')[0];
            if (!acc[dateKey]) acc[dateKey] = [];
            acc[dateKey].push(post);
            return acc;
        }, {} as Record<string, Post[]>);

        return (
            <div className="grid grid-cols-7 gap-px bg-slate-200 border-t border-l border-slate-200">
                {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(d => <div key={d} className="text-center font-semibold text-xs text-slate-500 py-2 bg-slate-50 border-b border-r border-slate-200">{d}</div>)}
                {days.map((d) => {
                    const dateKey = d.toISOString().split('T')[0];
                    return (
                        <div key={dateKey} className={`relative pt-8 p-1.5 bg-white border-b border-r border-slate-200 min-h-[140px] transition-colors ${d.getMonth() !== currentDate.getMonth() ? 'bg-slate-50/70' : ''}`}
                             onDragOver={(e) => e.preventDefault()}
                             onDrop={(e) => handleDrop(e, d)}>
                             <span className={`absolute top-1.5 left-1.5 text-xs font-semibold ${isToday(d) ? 'bg-purple-600 text-white rounded-full w-5 h-5 flex items-center justify-center' : 'text-slate-500'}`}>{d.getDate()}</span>
                             <div className="space-y-1">{postsByDate[dateKey]?.map(p => <CalendarPostItem key={p.id} post={p} onDragStart={handleDragStart} onClick={onPostClick} isBeingDragged={draggedPostId === p.id} />)}</div>
                        </div>
                    );
                })}
            </div>
        );
    };
    
    const renderTimelineView = (days: Date[]) => {
        return (
             <div className="grid border-l border-t border-slate-200" style={{gridTemplateColumns: `auto repeat(${days.length}, minmax(0, 1fr))`}}>
                 <div className="row-span-1"></div>
                 {days.map(d => <div key={d.toISOString()} className="text-center p-2 border-b border-r border-slate-200 bg-slate-50">
                     <p className="font-semibold text-slate-700">{d.toLocaleDateString('default', {weekday: 'short'})}</p>
                     <p className={`text-2xl font-bold ${isToday(d) ? 'text-purple-600' : 'text-slate-800'}`}>{d.getDate()}</p>
                 </div>)}

                 {hours.map(hour => (
                     <React.Fragment key={hour}>
                         <div className="text-right -mt-2 pr-2">
                             <span className="text-xs text-slate-400">{hour === 0 ? '12 AM' : hour < 12 ? `${hour} AM` : hour === 12 ? '12 PM' : `${hour-12} PM`}</span>
                         </div>
                         {days.map(d => {
                            const dayPosts = posts.filter(p => p.scheduledDate && new Date(p.scheduledDate).toDateString() === d.toDateString());
                            return (
                                <div key={d.toISOString()} className="relative border-r border-b border-slate-200 h-16"
                                     onDragOver={e => e.preventDefault()}
                                     onDrop={e => handleDrop(e, d, hour)}>
                                     {dayPosts.filter(p => new Date(p.scheduledDate!).getHours() === hour).map(p => {
                                         const top = (new Date(p.scheduledDate!).getMinutes() / 60) * 100;
                                         return (
                                             <div key={p.id} className="absolute w-full px-0.5" style={{ top: `${top}%` }}>
                                                 <CalendarPostItem post={p} onDragStart={handleDragStart} onClick={onPostClick} isBeingDragged={draggedPostId === p.id} showTime={true} />
                                             </div>
                                         );
                                     })}
                                </div>
                             )
                         })}
                     </React.Fragment>
                 ))}
             </div>
        );
    }
    
    const isToday = (date: Date) => {
        const today = new Date();
        return date.toDateString() === today.toDateString();
    };
    
    const getHeaderTitle = () => {
        if (viewMode === 'month') return currentDate.toLocaleString('default', { month: 'long', year: 'numeric' });
        const start = viewMode === 'week' ? new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate() - currentDate.getDay()) : currentDate;
        const end = viewMode === 'week' ? new Date(start.getTime()) : currentDate;
        if(viewMode === 'week') end.setDate(end.getDate() + 6);

        if (start.getMonth() === end.getMonth()) {
            return `${start.toLocaleString('default', {month: 'long'})} ${start.getDate()} - ${end.getDate()}, ${start.getFullYear()}`;
        }
        return `${start.toLocaleString('default', {month: 'short', day: 'numeric'})} - ${end.toLocaleString('default', {month: 'short', day: 'numeric'})}, ${end.getFullYear()}`;
    };

    const daysToRender = useMemo(() => {
        if (viewMode === 'day') return [currentDate];
        if (viewMode === 'week') {
            const startOfWeek = new Date(currentDate);
            startOfWeek.setDate(currentDate.getDate() - currentDate.getDay());
            return Array.from({length: 7}, (_, i) => {
                const day = new Date(startOfWeek);
                day.setDate(day.getDate() + i);
                return day;
            });
        }
        return [];
    }, [currentDate, viewMode]);

    return (
        <div className="bg-white rounded-lg border border-slate-200 shadow-sm p-4 h-full flex flex-col">
             <div className="flex justify-between items-center mb-4 px-2 flex-shrink-0">
                <h2 className="text-lg font-bold text-slate-800">{getHeaderTitle()}</h2>
                <div className="flex items-center space-x-1">
                    <Button onClick={() => handleDateChange(-1)} variant="ghost" size="sm" className="p-2 !rounded-full"><ChevronDownIcon className="w-5 h-5 rotate-90" title="Previous" /></Button>
                    <Button onClick={handleToday} variant="outline" size="sm">Today</Button>
                    <Button onClick={() => handleDateChange(1)} variant="ghost" size="sm" className="p-2 !rounded-full"><ChevronDownIcon className="w-5 h-5 -rotate-90" title="Next" /></Button>
                </div>
            </div>
            <div className="flex-grow overflow-auto">
                {viewMode === 'month' ? renderMonthView() : renderTimelineView(daysToRender)}
            </div>
        </div>
    );
};

const CalendarPostItem: React.FC<{post: Post; onDragStart: (e: React.DragEvent<HTMLDivElement>, post: Post) => void; onClick: (post: Post) => void; isBeingDragged: boolean; showTime?: boolean}> = ({ post, onDragStart, onClick, isBeingDragged, showTime }) => (
    <div
        draggable
        onDragStart={(e) => onDragStart(e, post)}
        onClick={() => onClick(post)}
        className={`flex items-center space-x-1.5 p-1 rounded-md bg-fuchsia-50 text-fuchsia-800 text-xs font-medium cursor-grab hover:bg-fuchsia-100 active:cursor-grabbing transition-all ${isBeingDragged ? 'opacity-50 scale-95' : ''}`}
    >
        <PlatformIcon platform={post.platform} className="w-3 h-3 flex-shrink-0" />
        <p className="truncate flex-grow">{post.caption}</p>
        {showTime && <span className="text-fuchsia-600/80 font-semibold flex-shrink-0">{new Date(post.scheduledDate!).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit', hour12: false})}</span>}
    </div>
);


export default CalendarView;