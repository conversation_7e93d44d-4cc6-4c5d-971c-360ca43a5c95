import React, { useMemo, useState, useEffect, useCallback } from 'react';
import { Post } from '../types';
import { COSTS } from '../contexts/AppContext';
import Loader from './ui/Loader';
import { HeartIcon } from './icons/HeartIcon';
import { CommentIcon } from './icons/CommentIcon';
import { RetryIcon } from './icons/RetryIcon';
import { CheckCircleIcon } from './icons/CheckCircleIcon';
import Button from './ui/Button';
import Accordion from './ui/Accordion';
import PlatformIcon from './ui/PlatformIcon';
import { usePosts, useSettings, useUI } from '../contexts/AppContext';

interface PostItemProps {
    post: Post;
}

const PostItem: React.FC<PostItemProps> = ({ post }) => {
    const { settings, loadingStates } = useSettings();
    const { handleGenerateImageForPost } = usePosts();
    const { setSelectedPost } = useUI();
    
    const [error, setError] = useState<string | null>(null);
    const isLoading = loadingStates.image === post.id;

    const triggerImageGeneration = useCallback(async () => {
        if (isLoading) return;

        if (settings.role !== 'admin' && settings.credits < COSTS.IMAGE_GENERATION_PER_IMAGE) {
            setError(`You need ${COSTS.IMAGE_GENERATION_PER_IMAGE} credits to generate an image.`);
            return;
        }

        setError(null);

        try {
            await handleGenerateImageForPost(post, post.image_prompt, 1);
        } catch (err) {
            setError("We couldn't get an image right now. Please try again.");
        }
    }, [post, settings, handleGenerateImageForPost, isLoading]);
    
    useEffect(() => {
        if (post.imgSrc) setError(null);
    }, [post.imgSrc]);

    // Images are now generated strictly on demand via button click (no scroll-triggered generation).


    return (
        <div className="aspect-square bg-slate-200 cursor-pointer overflow-hidden group relative rounded-md" onClick={() => post.imgSrc && !error && setSelectedPost(post)} tabIndex={0}>
            {isLoading && !post.imgSrc && <div className="w-full h-full flex items-center justify-center"><Loader size="sm" /></div>}
            
            {error && (
                <div className="w-full h-full flex flex-col items-center justify-center bg-slate-50 text-slate-700 p-3 text-center" title={error}>
                    <p className="font-semibold text-sm mb-2">Image not available</p>
                    <p className="text-xs mb-3">{error}</p>
                    {!error.includes('credits') && (
                        <Button onClick={(e) => { e.stopPropagation(); triggerImageGeneration(); }} size="sm" variant="secondary">
                            <RetryIcon className="w-4 h-4 mr-1"/>Try again
                        </Button>
                    )}
                </div>
            )}

            {!isLoading && post.imgSrc && (
                 <>
                    <img src={post.imgSrc} alt={`Post thumbnail for ${post.platform}`} className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"/>
                    <div className="absolute inset-0 bg-black/40 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                        {post.status === 'published' ? (
                            <div className="flex flex-col items-center text-white">
                                <CheckCircleIcon className="w-12 h-12 text-green-400" />
                                <span className="font-bold mt-2">Published</span>
                            </div>
                        ) : (
                            <div className="flex items-center space-x-6 text-white font-bold">
                                <div className="flex items-center"><HeartIcon className="w-6 h-6 mr-2"/><span>{post.likes.toLocaleString()}</span></div>
                                <div className="flex items-center"><CommentIcon className="w-6 h-6 mr-2"/><span>{post.comments.toLocaleString()}</span></div>
                            </div>
                        )}
                    </div>
                    <div className="absolute top-2 right-2 bg-white/80 backdrop-blur-sm p-1 rounded-full shadow"><PlatformIcon platform={post.platform} className="w-4 h-4" /></div>
                </>
            )}

            {!post.imgSrc && !isLoading && !error && (
                <div className="w-full h-full bg-slate-100 flex flex-col items-center justify-center p-2">
                    <p className="text-xs text-slate-500 mb-2">No image yet</p>
                    <Button onClick={(e) => { e.stopPropagation(); triggerImageGeneration(); }} size="sm">
                        Generate Image ({COSTS.IMAGE_GENERATION_PER_IMAGE} cr)
                    </Button>
                </div>
            )}
        </div>
    );
};


const PostGrid: React.FC = () => {
    const { posts } = usePosts();
    const [platformFilter, setPlatformFilter] = useState('all');
    const [statusFilter, setStatusFilter] = useState('all');
    const [sortBy, setSortBy] = useState('date');

    const platformFilters = useMemo(() => {
        const platformsInPosts = new Set(posts.map(p => p.platform));
        if (platformsInPosts.size <= 1) return [];
        return ['all', ...Array.from(platformsInPosts).sort()];
    }, [posts]);

    const statusFilters = useMemo(() => {
        const statusesInPosts = new Set(posts.map(p => p.status));
        if (statusesInPosts.size <= 1) return [];
        return ['all', ...Array.from(statusesInPosts).sort()];
    }, [posts]);

    const filteredPosts = useMemo(() => {
        let filtered = posts;
        if (platformFilter !== 'all') {
            filtered = filtered.filter(p => p.platform === platformFilter);
        }
        if (statusFilter !== 'all') {
            filtered = filtered.filter(p => p.status === statusFilter);
        }
        return filtered;
    }, [posts, platformFilter, statusFilter]);

    const sortedPosts = useMemo(() => {
        return [...filteredPosts].sort((a, b) => {
            if (sortBy === 'date') {
                return new Date(b.date).getTime() - new Date(a.date).getTime();
            }
            if (sortBy === 'platform') {
                return a.platform.localeCompare(b.platform);
            }
            if (sortBy === 'status') {
                return a.status.localeCompare(b.status);
            }
            return 0;
        });
    }, [filteredPosts, sortBy]);

    const groupedPosts = useMemo(() => {
        const groups: { [weekId: string]: Post[] } = {};
        sortedPosts.forEach(post => {
            const weekId = post.weekId || 'Uncategorized';
            if (!groups[weekId]) groups[weekId] = [];
            groups[weekId].push(post);
        });
        return groups;
    }, [sortedPosts]);

    const uncategorizedPosts = groupedPosts['Uncategorized'] || [];
    delete groupedPosts['Uncategorized'];
    const generatedPostsGrouped = Object.entries(groupedPosts).sort(([, postsA], [, postsB]) => new Date(postsB[0].date).getTime() - new Date(postsA[0].date).getTime());
    
    return (
        <div>
             <div className="flex justify-between items-center mb-6">
                <div className="flex items-center space-x-4">
                    {platformFilters.length > 0 && (
                        <div className="flex items-center space-x-1 bg-white p-1 rounded-lg border border-slate-200 shadow-sm">
                            {platformFilters.map(p => <Button key={p} onClick={() => setPlatformFilter(p)} variant={platformFilter === p ? 'accent' : 'ghost'} size="sm" className={`capitalize ${platformFilter === p ? 'text-white shadow-sm' : 'text-slate-600 hover:bg-slate-100'}`}>{p === 'x-twitter' ? 'X' : p}</Button>)}
                        </div>
                    )}
                    {statusFilters.length > 0 && (
                        <div className="flex items-center space-x-1 bg-white p-1 rounded-lg border border-slate-200 shadow-sm">
                            {statusFilters.map(s => <Button key={s} onClick={() => setStatusFilter(s)} variant={statusFilter === s ? 'accent' : 'ghost'} size="sm" className={`capitalize ${statusFilter === s ? 'text-white shadow-sm' : 'text-slate-600 hover:bg-slate-100'}`}>{s}</Button>)}
                        </div>
                    )}
                </div>
                <div className="flex items-center space-x-2">
                    <label htmlFor="sortBy" className="text-sm font-medium text-slate-600">Sort by:</label>
                    <select id="sortBy" value={sortBy} onChange={(e) => setSortBy(e.target.value)} className="input text-sm">
                        <option value="date">Date</option>
                        <option value="platform">Platform</option>
                        <option value="status">Status</option>
                    </select>
                </div>
            </div>
            
            {uncategorizedPosts.length > 0 &&
                <div className="mb-8">
                    <h3 className="font-bold text-lg text-slate-700 mb-3">Manually Added Posts</h3>
                    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-2 md:gap-4">
                        {uncategorizedPosts.map(p => <PostItem key={p.id} post={p} />)}
                    </div>
                </div>
            }
            
            <div className="space-y-6">
                {generatedPostsGrouped.map(([weekId, weekPosts]) => (
                     <Accordion key={weekId} title={`${weekId} (${weekPosts.length} posts)`} initiallyOpen={true}>
                        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-2 md:gap-4">
                             {weekPosts.map(p => <PostItem key={p.id} post={p} />)}
                        </div>
                    </Accordion>
                ))}
            </div>
             {posts.length === 0 && (
                <div className="text-center py-20 text-slate-500 bg-white rounded-lg border border-dashed border-slate-300">
                    <h3 className="text-lg font-semibold text-slate-700">Your Grid is Empty</h3><p className="mt-1">Start by generating a content plan in the "Strategy Brief"!</p><div className="text-5xl mt-4">📸</div>
                </div>
            )}
        </div>
    );
};

export default PostGrid;
