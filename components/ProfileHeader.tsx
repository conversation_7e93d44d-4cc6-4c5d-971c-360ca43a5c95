import React, { useState } from 'react';
import { useUI } from '../contexts/AppContext';
import { LightbulbIcon } from './icons/LightbulbIcon';
import { CheckCircleIcon } from './icons/CheckCircleIcon';
import { GiftIcon } from './icons/GiftIcon';

const tips = [
    "Engage with your audience by asking questions in your captions.",
    "Use high-quality images to make your posts stand out.",
    "Post consistently to keep your audience engaged.",
    "Use relevant hashtags to reach a wider audience.",
    "Collaborate with other creators to grow your audience.",
];

const goals = [
    "Generate 3 new posts.",
    "Engage with 5 followers.",
    "Plan your content for the next week.",
    "Research new content ideas.",
    "Experiment with a new content format.",
];

const ProfileHeader: React.FC = () => {
    const { setPlanSummaryOpen } = useUI();
    const [streak, setStreak] = useState(0);
    const [goalCompleted, setGoalCompleted] = useState(false);

    const today = new Date().getDay();
    const tip = tips[today % tips.length];
    const goal = goals[today % goals.length];

    const handleGoalCompletion = () => {
        if (!goalCompleted) {
            setStreak(streak + 1);
            setGoalCompleted(true);
        }
    };

    return (
        <div className="flex justify-between items-center p-4 bg-white border-b border-slate-200">
            <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                    <LightbulbIcon className="w-6 h-6 text-yellow-400" />
                    <div>
                        <p className="text-sm font-semibold text-slate-800">Tip of the Day</p>
                        <p className="text-xs text-slate-500">{tip}</p>
                    </div>
                </div>
                <div className="flex items-center space-x-2">
                    <CheckCircleIcon className={`w-6 h-6 ${goalCompleted ? 'text-green-500' : 'text-slate-400'}`} />
                    <div>
                        <p className="text-sm font-semibold text-slate-800">Daily Goal</p>
                        <p className="text-xs text-slate-500">{goal}</p>
                    </div>
                    {!goalCompleted && <button onClick={handleGoalCompletion} className="text-xs text-purple-600 hover:underline">Mark as Complete</button>}
                </div>
                <div className="flex items-center space-x-2">
                    <GiftIcon className="w-6 h-6 text-red-500" />
                    <div>
                        <p className="text-sm font-semibold text-slate-800">Streak</p>
                        <p className="text-xs text-slate-500">{streak} days</p>
                    </div>
                </div>
            </div>
            <button onClick={() => setPlanSummaryOpen(true)} className="text-sm font-medium text-slate-600 hover:text-slate-900">
                Plan Summary
            </button>
        </div>
    );
};

export default ProfileHeader;
